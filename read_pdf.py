import PyPDF2
import sys

def read_pdf(file_path):
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            print(f"Number of pages: {len(pdf_reader.pages)}")
            print("=" * 50)
            
            full_text = ""
            for page_num, page in enumerate(pdf_reader.pages):
                text = page.extract_text()
                print(f"\n--- Page {page_num + 1} ---")
                print(text)
                full_text += f"\n--- Page {page_num + 1} ---\n" + text
                
            return full_text
            
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return None

if __name__ == "__main__":
    pdf_path = "BI350 ASSESSMENT1 2025.pdf"
    read_pdf(pdf_path)
