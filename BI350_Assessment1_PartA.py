import pandas as pd
import numpy as np
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
import os

# Load the Excel file
file_path = "Randomvillerawdata.xlsx"
excel_file = pd.ExcelFile(file_path)

# Load the 'Data' sheet
df = excel_file.parse('Data')

# Rename columns to match actual columns
df.columns = ['ID', 'Sex', 'SocioEcon', 'AlcoholUnits', 'Smokes', 'Children',
              'InternetAccess', 'Income', 'HabitableRooms', 'Height', 'Weight']

# --- Create a new Excel workbook with formulas ---
output_path = "BI350_PartA_Samples.xlsx"
wb = Workbook()

# Remove default sheet
wb.remove(wb.active)

# --- (a) Simple Random Sample of size 50 ---
sample_a = df.sample(n=50, random_state=1)
ws_a = wb.create_sheet("Sample_A_SRS_50")

# Add formula description
ws_a.append(["SIMPLE RANDOM SAMPLING (SRS) FORMULA"])
ws_a.append(["Method: =INDEX(Data!$A$2:$K$1001, RANDBETWEEN(1,1000), 0)"])
ws_a.append(["Actual implementation: pandas.sample(n=50, random_state=1)"])
ws_a.append(["", "Random seed ensures reproducibility"])
ws_a.append([])

# Add sample data
for r in dataframe_to_rows(sample_a, index=False, header=True):
    ws_a.append(r)

# --- (b) Unrestricted Random Sample of size 40 ---
sample_b = df.sample(n=40, random_state=2)
ws_b = wb.create_sheet("Sample_B_Unrestricted_40")

# Add formula description
ws_b.append(["UNRESTRICTED RANDOM SAMPLING FORMULA"])
ws_b.append(["Method: Same as SRS but with different sample size and seed"])
ws_b.append(["Actual implementation: pandas.sample(n=40, random_state=2)"])
ws_b.append(["", "Each observation has equal probability of selection: P(i) = 40/1000"])
ws_b.append([])

# Add sample data
for r in dataframe_to_rows(sample_b, index=False, header=True):
    ws_b.append(r)

# --- (c) Stratified Sample by Sex, Proportional Allocation, n=50 ---
strata_c = df['Sex'].value_counts(normalize=True)
n_male = int(round(50 * strata_c.get(1, 0)))
n_female = 50 - n_male

sample_c_male = df[df['Sex'] == 1].sample(n=n_male, random_state=3)
sample_c_female = df[df['Sex'] == 2].sample(n=n_female, random_state=3)
sample_c = pd.concat([sample_c_male, sample_c_female])

ws_c = wb.create_sheet("Sample_C_Stratified_Sex_50")

# Add formula description
ws_c.append(["STRATIFIED SAMPLING (BY SEX) FORMULAS"])
ws_c.append(["Proportional Allocation:"])
male_count = len(df[df['Sex']==1])
ws_c.append(["Male: n₁ = (N₁/N) * n =", f"({male_count}/1000) * 50 = {n_male}"])
ws_c.append(["Female: n₂ = n - n₁ =", f"50 - {n_male} = {n_female}"])
ws_c.append(["Selection: =FILTER(Data!A:K, Data!B=1) then SRS within stratum"])
ws_c.append(["Actual implementation: Stratify by Sex, then sample within each stratum"])
ws_c.append([])

# Add sample data
for r in dataframe_to_rows(sample_c, index=False, header=True):
    ws_c.append(r)

# --- (d) Stratified Sample by Socio-economic Group using Neyman Allocation, n=100 ---
group_sizes = df['SocioEcon'].value_counts().sort_index()
group_std_devs = {1: 1.11, 2: 1.22, 3: 0.84}  # Provided std devs

# Calculate weights and fractional allocation
weights = {group: group_sizes[group] * group_std_devs[group] for group in group_std_devs}
total_weight = sum(weights.values())
alloc_float = {group: 100 * weights[group] / total_weight for group in weights}

# Integer allocation with remainder distribution
alloc_base = {group: int(np.floor(alloc_float[group])) for group in alloc_float}
remainder = 100 - sum(alloc_base.values())

# Sort groups by fractional part
fractional_parts = sorted(
    alloc_float.items(), 
    key=lambda x: x[1] - np.floor(x[1]), 
    reverse=True
)

# Distribute remainder
neyman_alloc = alloc_base.copy()
for i in range(remainder):
    group = fractional_parts[i][0]
    neyman_alloc[group] += 1

# Sample from each socio-economic group
samples_d = []
for group, size in neyman_alloc.items():
    group_df = df[df['SocioEcon'] == group]
    samples_d.append(group_df.sample(n=size, random_state=4, replace=False))
    
sample_d = pd.concat(samples_d)

ws_d = wb.create_sheet("Sample_D_Neyman_Strat_100")

# Add formula description
ws_d.append(["NEYMAN ALLOCATION FORMULAS"])
ws_d.append(["Group", "Population (Nₕ)", "Std Dev (Sₕ)", "Weight (Nₕ×Sₕ)", "Fractional Allocation", "Final Allocation"])
for group in sorted(neyman_alloc.keys()):
    ws_d.append([
        group,
        group_sizes[group],
        group_std_devs[group],
        weights[group],
        alloc_float[group],
        neyman_alloc[group]
    ])

ws_d.append([])
ws_d.append(["Total Weight = Σ(Nₕ×Sₕ) =", total_weight])
ws_d.append(["Allocation Formula: nₕ = n × (Nₕ×Sₕ) / Σ(Nₕ×Sₕ)"])
ws_d.append(["Actual implementation: Floor allocation + remainder to largest fractions"])
ws_d.append([])

# Add sample data
for r in dataframe_to_rows(sample_d, index=False, header=True):
    ws_d.append(r)

# --- Save workbook ---
wb.save(output_path)
print(f"All samples with formulas saved to: {output_path}")
print("Neyman allocation breakdown:")
for group, size in neyman_alloc.items():
    print(f"  SocioEcon {group}: {size} samples")