import pandas as pd
import numpy as np
import openpyxl
from openpyxl import Workbook

# Load the Excel file
file_path = "Randomvillerawdata.xlsx"
excel_file = pd.ExcelFile(file_path)

# Load the 'Data' sheet
df = excel_file.parse('Data')

# Rename columns to match actual columns
df.columns = ['ID', 'Sex', 'SocioEcon', 'AlcoholUnits', 'Smokes', 'Children',
              'InternetAccess', 'Income', 'HabitableRooms', 'Height', 'Weight']

# --- Create a new Excel workbook with active formulas ---
output_path = "BI350_PartA_Samples_Fixed.xlsx"
wb = Workbook()

# Remove default sheet
wb.remove(wb.active)

# --- Helper function to add formulas to Excel with proper alignment ---
def add_formula_sheet(ws, title, formula_rows, sample_df, sample_size):
    """Add formula explanations and sample data to worksheet with proper alignment"""
    ws.title = title

    # Add formula header and explanations (rows 1-8)
    ws.append(["SAMPLING FORMULAS AND METHODOLOGY"])
    for row in formula_rows:
        ws.append(row)
    ws.append([])  # Empty row for separation

    # Explicitly add headers to row 9
    headers = list(sample_df.columns)
    extended_headers = headers + ["RAND()", "RANK()", "Selected?"]

    # Write headers explicitly to row 9
    header_row = 9
    for col_idx, header in enumerate(extended_headers):
        ws.cell(row=header_row, column=col_idx + 1, value=header)

    # Add sample data with formulas (starting from row 10)
    start_row = 10
    for i, (_, row) in enumerate(sample_df.iterrows()):
        row_num = start_row + i
        data_row = row.tolist()

        # Add the sample data
        for col_idx, value in enumerate(data_row):
            ws.cell(row=row_num, column=col_idx + 1, value=value)

        # Add RAND() formula
        rand_col = len(headers) + 1
        ws.cell(row=row_num, column=rand_col, value="=RAND()")

        # Add RANK() formula
        rank_col = len(headers) + 2
        rand_col_letter = openpyxl.utils.get_column_letter(rand_col)
        end_row = start_row + len(sample_df) - 1
        ws.cell(row=row_num, column=rank_col,
                value=f'=RANK({rand_col_letter}{row_num},${rand_col_letter}${start_row}:${rand_col_letter}${end_row})')

        # Add selection indicator formula
        select_col = len(headers) + 3
        rank_col_letter = openpyxl.utils.get_column_letter(rank_col)
        ws.cell(row=row_num, column=select_col,
                value=f'=IF({rank_col_letter}{row_num}<={sample_size},"YES","NO")')

# --- (a) Simple Random Sample of size 50 ---
sample_a = df.sample(n=50, random_state=1)
formulas_a = [
    ["Method: Simple Random Sampling"],
    ["Population Size: 1000"],
    ["Sample Size: 50"],
    ["Process: Assign RAND() to each row, RANK() the random numbers, select top 50"],
    ["RAND() Formula: =RAND() - generates random number 0-1"],
    ["RANK() Formula: =RANK(cell,range) - ranks random numbers"],
    ["Selection: =IF(RANK<=50,\"YES\",\"NO\") - selects top 50 ranked items"]
]
ws_a = wb.create_sheet("Sample_A_SRS_50")
add_formula_sheet(ws_a, "Sample_A_SRS_50", formulas_a, sample_a, 50)

# --- (b) Unrestricted Random Sample of size 40 ---
sample_b = df.sample(n=40, random_state=2)
formulas_b = [
    ["Method: Unrestricted Random Sampling"],
    ["Population Size: 1000"],
    ["Sample Size: 40"],
    ["Process: Assign RAND() to each row, RANK() the random numbers, select top 40"],
    ["RAND() Formula: =RAND() - generates random number 0-1"],
    ["RANK() Formula: =RANK(cell,range) - ranks random numbers"],
    ["Selection: =IF(RANK<=40,\"YES\",\"NO\") - selects top 40 ranked items"]
]
ws_b = wb.create_sheet("Sample_B_Unrestricted_40")
add_formula_sheet(ws_b, "Sample_B_Unrestricted_40", formulas_b, sample_b, 40)

# --- (c) Stratified Sample by Sex, Proportional Allocation, n=50 ---
strata_c = df['Sex'].value_counts(normalize=True)
n_male = int(round(50 * strata_c.get(1, 0)))
n_female = 50 - n_male

sample_c_male = df[df['Sex'] == 1].sample(n=n_male, random_state=3)
sample_c_female = df[df['Sex'] == 2].sample(n=n_female, random_state=3)
sample_c = pd.concat([sample_c_male, sample_c_female])

male_count = len(df[df['Sex']==1])
formulas_c = [
    ["Method: Stratified Sampling by Sex (Proportional Allocation)"],
    [f"Male Population: {male_count}, Female Population: {1000-male_count}"],
    [f"Male Sample: n₁ = ({male_count}/1000) × 50 = {n_male}"],
    [f"Female Sample: n₂ = 50 - {n_male} = {n_female}"],
    ["Process: Apply SRS within each sex stratum"],
    ["RAND() Formula: =RAND() - within each stratum"],
    ["Selection: =IF(RANK<=nₕ,\"YES\",\"NO\") - where nₕ is stratum sample size"]
]
ws_c = wb.create_sheet("Sample_C_Stratified_Sex_50")
add_formula_sheet(ws_c, "Sample_C_Stratified_Sex_50", formulas_c, sample_c, 50)

# --- (d) Stratified Sample by Socio-economic Group using Neyman Allocation, n=100 ---
group_sizes = df['SocioEcon'].value_counts().sort_index()
group_std_devs = {1: 1.11, 2: 1.22, 3: 0.84}

# Calculate weights and fractional allocation
weights = {group: group_sizes[group] * group_std_devs[group] for group in group_std_devs}
total_weight = sum(weights.values())
alloc_float = {group: 100 * weights[group] / total_weight for group in weights}

# Integer allocation with remainder distribution
alloc_base = {group: int(np.floor(alloc_float[group])) for group in alloc_float}
remainder = 100 - sum(alloc_base.values())

# Sort groups by fractional part
fractional_parts = sorted(
    alloc_float.items(),
    key=lambda x: x[1] - np.floor(x[1]),
    reverse=True
)

# Distribute remainder
neyman_alloc = alloc_base.copy()
for i in range(remainder):
    group = fractional_parts[i][0]
    neyman_alloc[group] += 1

# Sample from each socio-economic group
samples_d = []
for group, size in neyman_alloc.items():
    group_df = df[df['SocioEcon'] == group]
    samples_d.append(group_df.sample(n=size, random_state=4, replace=False))

sample_d = pd.concat(samples_d)

formulas_d = [
    ["Method: Stratified Sampling by Socio-Economic Group (Neyman Allocation)"],
    [f"Group 1: N₁={group_sizes[1]}, S₁={group_std_devs[1]}, n₁={neyman_alloc[1]}"],
    [f"Group 2: N₂={group_sizes[2]}, S₂={group_std_devs[2]}, n₂={neyman_alloc[2]}"],
    [f"Group 3: N₃={group_sizes[3]}, S₃={group_std_devs[3]}, n₃={neyman_alloc[3]}"],
    [f"Formula: nₕ = n × (Nₕ×Sₕ) / Σ(Nₕ×Sₕ), Total Weight = {total_weight}"],
    ["Process: Apply SRS within each socio-economic group"],
    ["Selection: =IF(RANK<=nₕ,\"YES\",\"NO\") - where nₕ varies by group"]
]
ws_d = wb.create_sheet("Sample_D_Neyman_Strat_100")
add_formula_sheet(ws_d, "Sample_D_Neyman_Strat_100", formulas_d, sample_d, 100)

# --- Save workbook ---
wb.save(output_path)
print(f"All samples with properly aligned formulas saved to: {output_path}")
print("Neyman allocation breakdown:")
for group, size in neyman_alloc.items():
    print(f"  SocioEcon {group}: {size} samples")
