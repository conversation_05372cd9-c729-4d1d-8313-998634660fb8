import pandas as pd
import numpy as np
import openpyxl
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
import os

# Load the Excel file
file_path = "Copy of Randomvillerawdata.xlsx"
excel_file = pd.ExcelFile(file_path)

# Load the 'Data' sheet
df = excel_file.parse('Data')

# Rename columns to match actual columns
df.columns = ['ID', 'Sex', 'SocioEcon', 'AlcoholUnits', 'Smokes', 'Children',
              'InternetAccess', 'Income', 'HabitableRooms', 'Height', 'Weight']

# --- Create a new Excel workbook with active formulas ---
output_path = "BI350_PartA_Samples.xlsx"
wb = Workbook()

# Remove default sheet
wb.remove(wb.active)

# --- Helper function to add formulas to Excel ---
def add_formula_sheet(ws, title, formula_rows, sample_df):
    """Add formula explanations and sample data to worksheet"""
    ws.title = title
    
    # Add formula header
    ws.append(["SAMPLING FORMULAS"])
    for row in formula_rows:
        ws.append(row)
    ws.append([])
    
    # Add sample data with formulas
    headers = list(sample_df.columns)
    ws.append(headers)
    
    # Add data rows
    for idx, row in sample_df.iterrows():
        ws.append(row.tolist())
    
    # Add formula columns
    max_row = len(sample_df) + 5  # Header row + offset
    
    # Add RAND() formula column
    ws.cell(row=5, column=len(headers)+1, value="RAND() Formula")
    for i in range(6, max_row+1):
        ws.cell(row=i, column=len(headers)+1, value=f"=RAND()")
    
    # Add RANK() formula column
    ws.cell(row=5, column=len(headers)+2, value="RANK() Formula")
    rand_col_letter = openpyxl.utils.get_column_letter(len(headers)+1)
    for i in range(6, max_row+1):
        ws.cell(row=i, column=len(headers)+2, 
                value=f'=RANK({rand_col_letter}{i}, ${rand_col_letter}$6:${rand_col_letter}${max_row})')
    
    # Add selection indicator formula
    ws.cell(row=5, column=len(headers)+3, value="Selected?")
    rank_col_letter = openpyxl.utils.get_column_letter(len(headers)+2)
    for i in range(6, max_row+1):
        ws.cell(row=i, column=len(headers)+3, 
                value=f'=IF({rank_col_letter}{i} <= ${rank_col_letter}$2, "Yes", "No")')

# --- (a) Simple Random Sample of size 50 ---
sample_a = df.sample(n=50, random_state=1)
formulas_a = [
    ["Method: Assign random number to each individual in population"],
    ["Selection: Select rows with smallest 50 random numbers"],
    ["Population Size: 1000"],
    ["Sample Size: 50"],
    ["RAND() Formula: =RAND()"],
    ["RANK() Formula: =RANK(cell, range)"],
    ["Selection Formula: =IF(RANK <= 50, \"Yes\", \"No\")"]
]
ws_a = wb.create_sheet("Sample_A_SRS_50")
add_formula_sheet(ws_a, "Sample_A_SRS_50", formulas_a, sample_a)

# --- (b) Unrestricted Random Sample of size 40 ---
sample_b = df.sample(n=40, random_state=2)
formulas_b = [
    ["Method: Assign random number to each individual in population"],
    ["Selection: Select rows with smallest 40 random numbers"],
    ["Population Size: 1000"],
    ["Sample Size: 40"],
    ["RAND() Formula: =RAND()"],
    ["RANK() Formula: =RANK(cell, range)"],
    ["Selection Formula: =IF(RANK <= 40, \"Yes\", \"No\")"]
]
ws_b = wb.create_sheet("Sample_B_Unrestricted_40")
add_formula_sheet(ws_b, "Sample_B_Unrestricted_40", formulas_b, sample_b)

# --- (c) Stratified Sample by Sex, Proportional Allocation, n=50 ---
strata_c = df['Sex'].value_counts(normalize=True)
n_male = int(round(50 * strata_c.get(1, 0)))
n_female = 50 - n_male

sample_c_male = df[df['Sex'] == 1].sample(n=n_male, random_state=3)
sample_c_female = df[df['Sex'] == 2].sample(n=n_female, random_state=3)
sample_c = pd.concat([sample_c_male, sample_c_female])

male_count = len(df[df['Sex']==1])
formulas_c = [
    ["STRATIFIED SAMPLING (BY SEX) FORMULAS"],
    ["Proportional Allocation:"],
    [f"Male: n₁ = (N₁/N) * n = ({male_count}/1000) * 50 = {n_male}"],
    [f"Female: n₂ = n - n₁ = 50 - {n_male} = {n_female}"],
    ["Method per stratum: Assign random number, select smallest nₕ in stratum"],
    ["RAND() Formula: =RAND()"],
    ["RANK() Formula: =RANK(cell, range)"],
    ["Selection Formula: =IF(RANK <= nₕ, \"Yes\", \"No\")"]
]
ws_c = wb.create_sheet("Sample_C_Stratified_Sex_50")
add_formula_sheet(ws_c, "Sample_C_Stratified_Sex_50", formulas_c, sample_c)

# --- (d) Stratified Sample by Socio-economic Group using Neyman Allocation, n=100 ---
group_sizes = df['SocioEcon'].value_counts().sort_index()
group_std_devs = {1: 1.11, 2: 1.22, 3: 0.84}

# Calculate weights and fractional allocation
weights = {group: group_sizes[group] * group_std_devs[group] for group in group_std_devs}
total_weight = sum(weights.values())
alloc_float = {group: 100 * weights[group] / total_weight for group in weights}

# Integer allocation with remainder distribution
alloc_base = {group: int(np.floor(alloc_float[group])) for group in alloc_float}
remainder = 100 - sum(alloc_base.values())

# Sort groups by fractional part
fractional_parts = sorted(
    alloc_float.items(),
    key=lambda x: x[1] - np.floor(x[1]),
    reverse=True
)

# Distribute remainder
neyman_alloc = alloc_base.copy()
for i in range(remainder):
    group = fractional_parts[i][0]
    neyman_alloc[group] += 1

# Sample from each socio-economic group
samples_d = []
for group, size in neyman_alloc.items():
    group_df = df[df['SocioEcon'] == group]
    samples_d.append(group_df.sample(n=size, random_state=4, replace=False))

sample_d = pd.concat(samples_d)

formulas_d = [
    ["NEYMAN ALLOCATION FORMULAS"],
    ["Group", "Population (Nₕ)", "Std Dev (Sₕ)", "Weight (Nₕ×Sₕ)", "Fractional Allocation", "Final Allocation"]
]
for group in sorted(neyman_alloc.keys()):
    formulas_d.append([
        group,
        group_sizes[group],
        group_std_devs[group],
        weights[group],
        alloc_float[group],
        neyman_alloc[group]
    ])
formulas_d.extend([
    [],
    ["Total Weight = Σ(Nₕ×Sₕ) =", total_weight],
    ["Allocation Formula: nₕ = n × (Nₕ×Sₕ) / Σ(Nₕ×Sₕ)"],
    ["Method per stratum: Assign random number, select smallest nₕ in group"],
    ["RAND() Formula: =RAND()"],
    ["RANK() Formula: =RANK(cell, range)"],
    ["Selection Formula: =IF(RANK <= nₕ, \"Yes\", \"No\")"]
])
ws_d = wb.create_sheet("Sample_D_Neyman_Strat_100")
add_formula_sheet(ws_d, "Sample_D_Neyman_Strat_100", formulas_d, sample_d)

# --- Save workbook ---
wb.save(output_path)
print(f"All samples with active formulas saved to: {output_path}")
print("Neyman allocation breakdown:")
for group, size in neyman_alloc.items():
    print(f"  SocioEcon {group}: {size} samples")
