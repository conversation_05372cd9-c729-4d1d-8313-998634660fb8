import openpyxl

# Load the fixed workbook
wb = openpyxl.load_workbook('BI350_PartA_Samples_Fixed.xlsx')
print('=== DETAILED VERIFICATION ===')

# Check Sample A structure
ws = wb['Sample_A_SRS_50']
print('Sample A - Row by Row Analysis:')

for row in range(1, 12):
    row_data = []
    for col in range(1, 15):
        cell_value = ws.cell(row=row, column=col).value
        if cell_value is None:
            row_data.append('EMPTY')
        elif str(cell_value).startswith('='):
            row_data.append('FORMULA')
        else:
            row_data.append(str(cell_value)[:15])
    print(f'Row {row}: {row_data}')

print('\n=== SPECIFIC CHECKS ===')
print(f'Row 9 Column A: "{ws.cell(row=9, column=1).value}"')
print(f'Row 9 Column B: "{ws.cell(row=9, column=2).value}"')
print(f'Row 9 Column L: "{ws.cell(row=9, column=12).value}"')
print(f'Row 10 Column A: "{ws.cell(row=10, column=1).value}"')
print(f'Row 10 Column L: "{ws.cell(row=10, column=12).value}"')
